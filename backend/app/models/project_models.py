from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from enum import Enum


class ProjectType(str, Enum):
    AUTO = "auto"  # AI determines the best technology stack
    FULLSTACK = "fullstack"  # Complete full-stack application
    FRONTEND = "frontend"  # Frontend-only application
    BACKEND = "backend"  # Backend-only application
    MOBILE = "mobile"  # Mobile application
    DESKTOP = "desktop"  # Desktop application
    API = "api"  # API service
    MICROSERVICE = "microservice"  # Microservice architecture
    REACT = "react"
    PYTHON = "python"
    REACT_NATIVE = "react_native"
    NEXTJS = "nextjs"
    FASTAPI = "fastapi"
    DJANGO = "django"
    FLASK = "flask"
    VUE = "vue"
    ANGULAR = "angular"
    NODEJS = "nodejs"
    EXPRESS = "express"
    NESTJS = "nestjs"


class AIProvider(str, Enum):
    OPENAI = "openai"
    HUGGINGFACE = "huggingface"
    GROK = "grok"


class TechnologyStack(BaseModel):
    frontend: Optional[List[str]] = None
    backend: Optional[List[str]] = None
    database: Optional[List[str]] = None
    deployment: Optional[List[str]] = None
    testing: Optional[List[str]] = None
    styling: Optional[List[str]] = None
    state_management: Optional[List[str]] = None
    authentication: Optional[List[str]] = None
    additional_tools: Optional[List[str]] = None


class IntelligentProjectRequest(BaseModel):
    """Enhanced project request with intelligent prompt understanding"""

    prompt: str = Field(
        ...,
        min_length=10,
        max_length=5000,
        description="Natural language description of the project",
    )
    ai_provider: AIProvider = AIProvider.HUGGINGFACE
    project_name: Optional[str] = None  # AI can suggest if not provided
    preferred_technologies: Optional[List[str]] = None  # User preferences
    constraints: Optional[List[str]] = None  # Technical constraints
    target_platform: Optional[List[str]] = None  # web, mobile, desktop, etc.
    complexity_level: Optional[str] = Field(
        default="medium", description="simple, medium, complex, enterprise"
    )
    include_tests: bool = True
    include_documentation: bool = True
    include_deployment: bool = False
    include_ci_cd: bool = False


class ProjectAnalysis(BaseModel):
    """AI analysis of the project requirements"""

    project_type: str
    suggested_name: str
    technology_stack: TechnologyStack
    architecture_pattern: str  # MVC, microservices, serverless, etc.
    estimated_complexity: str
    key_features: List[str]
    technical_requirements: List[str]
    suggested_folder_structure: Dict[str, Any]
    dependencies: Dict[str, List[str]]  # frontend, backend, etc.
    reasoning: str  # AI's reasoning for technology choices


class FileModification(BaseModel):
    """Represents a file modification suggestion"""

    file_path: str
    action: str  # create, modify, delete
    content: Optional[str] = None
    reason: str
    priority: str = "medium"  # low, medium, high, critical


class ModificationSuggestion(BaseModel):
    """AI suggestions for project modifications"""

    suggestion_id: str
    title: str
    description: str
    category: str  # feature, bugfix, optimization, refactor
    files: List[FileModification]
    estimated_effort: str  # small, medium, large
    impact: str  # low, medium, high
    reasoning: str


class IntelligentProjectResponse(BaseModel):
    """Enhanced response for intelligent project generation"""

    project_id: str
    status: str
    message: str
    analysis: Optional[ProjectAnalysis] = None
    suggestions: Optional[List[ModificationSuggestion]] = None
    download_url: Optional[str] = None
    generated_files: Optional[List[str]] = None


class EnhancedGenerationStatus(BaseModel):
    """Enhanced generation status with modification capabilities"""

    project_id: str
    status: str  # "analyzing", "generating", "reviewing", "completed", "failed"
    progress: int  # 0-100
    current_step: str
    phase: str  # "analysis", "generation", "review", "modification"
    analysis: Optional[ProjectAnalysis] = None
    pending_modifications: Optional[List[ModificationSuggestion]] = None
    applied_modifications: Optional[List[str]] = None  # IDs of applied modifications
    estimated_time_remaining: Optional[int] = None  # seconds
    error_message: Optional[str] = None
    can_modify: bool = True  # Whether modifications are allowed in current state


class ModificationRequest(BaseModel):
    """Request to modify an existing project"""

    project_id: str
    modification_prompt: str = Field(..., min_length=5, max_length=2000)
    ai_provider: AIProvider = AIProvider.OPENAI
    apply_immediately: bool = False  # If True, apply without user confirmation


class ModificationResponse(BaseModel):
    """Response for modification requests"""

    modification_id: str
    project_id: str
    suggestions: List[ModificationSuggestion]
    status: str  # "pending", "ready", "applied", "failed"
    message: str


class ApplyModificationRequest(BaseModel):
    """Request to apply specific modifications"""

    project_id: str
    modification_ids: List[str]  # IDs of modifications to apply
    custom_changes: Optional[Dict[str, str]] = None  # file_path -> content overrides
